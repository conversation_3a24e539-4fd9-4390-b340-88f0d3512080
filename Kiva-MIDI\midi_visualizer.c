#include "raylib.h"
#include "raymath.h"
#include "OmniMIDI.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <limits.h>
#include <time.h>
#include <math.h>
#include <pthread.h>
#include <sched.h>  // For sched_yield

#define MAX_MIDI_NOTES 128
#define MAX_MIDI_TRACKS 65535
#define WINDOW_WIDTH 1280
#define WINDOW_HEIGHT 720
#define TEXTURE_HEIGHT 512
#define SECONDS_BUFFER 1.0f
#define MAX_PREPROCESSED_NOTES 1000000000  // Maximum number of preprocessed notes

#define TRACK_DATA 0
#define TRACK_TICK 1
#define TRACK_OFFS 2
#define TRACK_LEN 3
#define TRACK_MSG 4
#define TRACK_TMP 5
#define TRACK_LMSGLEN 6
#define TRACK_LMSG 7

#define PLAYER_TICK 0
#define PLAYER_MULTI 1
#define PLAYER_BPM 2
#define PLAYER_DELTATICK 3

typedef struct {
    bool active;
    float startTime;
    float releaseTime;
    int channel;
    int note;           // Note value
    int velocity;       // Note velocity
    int trackIndex;     // Which track this note belongs to
} NoteState;

typedef struct {
    int tick;           // MIDI tick when this event occurs
    int type;           // 0 = note on, 1 = note off, 2 = tempo change, 3 = controller event
    int channel;        // MIDI channel
    int note;           // Note value (for note events)
    int velocity;       // Note velocity (for note on)
    int trackIndex;     // Which track this event belongs to
    int tempo;          // Tempo value (for tempo events)
    int controller;     // Controller number (for controller events)
    int value;          // Controller value (for controller events)
    unsigned long rawMessage; // Raw MIDI message for other event types
} PreprocessedEvent;

typedef struct {
    unsigned char* data;
    int tick;
    int offset;
    int length;
    int message;
    int tmp;
    int longMsgLen;
    unsigned char* longMsg;
    bool active;
    Color color;              // Color for this track's notes
    Color outlineColor;       // Darker outline color
} MidiTrack;

typedef struct {
    int tick;
    float multi;
    int bpm;
    int deltaTick;
    int numTracks;
    MidiTrack* tracks;
    int timeDivision;
	Color channelColors[16];         // Colors for each MIDI channel
	Color channelOutlineColors[16];  // Outline colors for each channel
	bool useChannelColors;           // Toggle between track or channel colors
	float currentTempo;      // Current tempo in microseconds per quarter note
    int lastTempoTick;       // Tick position of the last tempo change
    float lastTempoTime;     // Real time position of last tempo change
    float playTime;
    float ticksPerSecond;
    bool kdmapiInitialized;
    bool isPaused;
    RenderTexture2D noteTexture;  // Main texture for note visualization
    float pixelsPerSecond;        // Conversion between time and pixels
    NoteState notes[MAX_MIDI_NOTES];  // Array of notes
    float textureScrollPos;       // Current scroll position of the texture
    float lastUpdateTime;         // Last time the texture was updated
	float lastEventProcessTime;  // Track when we last processed events
    bool continueProcessing;     // Flag to control processing thread
    pthread_t processingThread;  // Thread handle for event processing
    pthread_mutex_t playerMutex; // Mutex to protect player data

    // Preprocessed events
    PreprocessedEvent* preprocessedEvents;  // Array of preprocessed events (notes and tempo changes)
    int numPreprocessedEvents;              // Number of preprocessed events
    int currentEventIndex;                  // Current index in preprocessed events
    bool usePreprocessedEvents;             // Whether to use preprocessed events

    // Preprocessing progress
    int totalEventsToProcess;               // Estimated total events to process
    int eventsProcessed;                    // Number of events processed so far
    bool isPreprocessing;                   // Whether preprocessing is currently happening
} MidiPlayer;

int TrackDecodeVarLen(MidiTrack* track);
void TrackUpdateTick(MidiTrack* track);
void TrackUpdateCmd(MidiTrack* track);
void TrackUpdateMsg(MidiTrack* track, MidiPlayer* player);
void TrackExecuteCmd(MidiTrack* track, MidiPlayer* player);
void PreprocessAllEvents(MidiPlayer* player);
int ComparePreprocessedEvents(const void* a, const void* b);
void ProcessPreprocessedEvents(MidiPlayer* player);
MidiPlayer* LoadMidiFile(const char* filename);
bool UpdateMidiPlayer(MidiPlayer* player, float deltaTime);
void CleanupMidiPlayer(MidiPlayer* player);
bool InitializeAudio(MidiPlayer* player);
void ShutdownAudio(MidiPlayer* player);
void SendMidiNoteOn(int channel, int note, int velocity);
void SendMidiNoteOff(int channel, int note);
void ReleaseAllNotes(MidiPlayer* player);
void InitializeNoteTexture(MidiPlayer* player);
void UpdateNoteTexture(MidiPlayer* player);
void DrawNoteTexture(MidiPlayer* player);
Color DarkenColor(Color color, float amount);

// Function to compare preprocessed events for sorting
int ComparePreprocessedEvents(const void* a, const void* b) {
    PreprocessedEvent* eventA = (PreprocessedEvent*)a;
    PreprocessedEvent* eventB = (PreprocessedEvent*)b;

    // Sort by tick time first
    if (eventA->tick < eventB->tick) return -1;
    if (eventA->tick > eventB->tick) return 1;

    // If same tick, prioritize tempo changes first, then note-offs, then note-ons
    if (eventA->type != eventB->type) {
        // Tempo changes (2) come first
        if (eventA->type == 2) return -1;
        if (eventB->type == 2) return 1;

        // Then note-offs (1) before note-ons (0)
        return (eventA->type == 1) ? -1 : 1;
    }

    // If still tied, sort by note value for note events
    if (eventA->type < 2) {
        return eventA->note - eventB->note;
    }

    return 0;
}

// Function to preprocess all MIDI events
void PreprocessAllEvents(MidiPlayer* player) {
    printf("Preprocessing all MIDI events...\n");

    // Set preprocessing flag
    player->isPreprocessing = true;

    // Allocate memory for preprocessed events
    player->preprocessedEvents = (PreprocessedEvent*)malloc(sizeof(PreprocessedEvent) * MAX_PREPROCESSED_NOTES);
    player->numPreprocessedEvents = 0;
    player->currentEventIndex = 0;

    // Save original state
    int originalTick = player->tick;
    float originalPlayTime = player->playTime;

    // Reset all tracks to beginning
    for (int i = 0; i < player->numTracks; i++) {
        MidiTrack* track = &player->tracks[i];
        track->tick = 0;
        track->offset = 0;
        track->active = true;

        // Read the first delta time
        TrackUpdateTick(track);
    }

    // Estimate total events by summing track lengths
    player->totalEventsToProcess = 0;
    for (int i = 0; i < player->numTracks; i++) {
        // Rough estimate: assume 1 event per 3 bytes on average
        player->totalEventsToProcess += player->tracks[i].length / 3;
    }
    player->eventsProcessed = 0;

    // Process all tracks to extract events
    bool anyActive = true;
    player->tick = 0;

    // Create a min-heap of track indices, sorted by tick time
    // This is more efficient than scanning all tracks each time
    int* trackHeap = (int*)malloc(sizeof(int) * player->numTracks);
    int heapSize = 0;

    // Helper functions for heap operations
    #define PARENT(i) ((i - 1) / 2)
    #define LEFT_CHILD(i) (2 * i + 1)
    #define RIGHT_CHILD(i) (2 * i + 2)

    // Add all active tracks to the heap
    for (int i = 0; i < player->numTracks; i++) {
        MidiTrack* track = &player->tracks[i];
        if (track->active && track->offset < track->length) {
            // Add to heap
            int pos = heapSize++;
            trackHeap[pos] = i;

            // Bubble up
            while (pos > 0) {
                int parent = PARENT(pos);
                if (player->tracks[trackHeap[pos]].tick < player->tracks[trackHeap[parent]].tick) {
                    // Swap
                    int temp = trackHeap[pos];
                    trackHeap[pos] = trackHeap[parent];
                    trackHeap[parent] = temp;
                    pos = parent;
                } else {
                    break;
                }
            }
        }
    }

    while (heapSize > 0 && player->numPreprocessedEvents < MAX_PREPROCESSED_NOTES) {
        // Get the track with the earliest tick from the heap
        int earliestTrack = trackHeap[0];
        MidiTrack* track = &player->tracks[earliestTrack];

        // Remove from heap
        trackHeap[0] = trackHeap[--heapSize];

        // Bubble down
        int pos = 0;
        while (1) {
            int smallest = pos;
            int left = LEFT_CHILD(pos);
            int right = RIGHT_CHILD(pos);

            if (left < heapSize &&
                player->tracks[trackHeap[left]].tick < player->tracks[trackHeap[smallest]].tick) {
                smallest = left;
            }

            if (right < heapSize &&
                player->tracks[trackHeap[right]].tick < player->tracks[trackHeap[smallest]].tick) {
                smallest = right;
            }

            if (smallest != pos) {
                // Swap
                int temp = trackHeap[pos];
                trackHeap[pos] = trackHeap[smallest];
                trackHeap[smallest] = temp;
                pos = smallest;
            } else {
                break;
            }
        }

        // Process the event from the earliest track
        player->tick = track->tick;

        // Extract the event
        TrackUpdateCmd(track);
        TrackUpdateMsg(track, player);

        // Update progress counter
        player->eventsProcessed++;

        // Check event type
        unsigned char cmd = track->message & 0xFF;
        unsigned char channel = cmd & 0x0F;
        unsigned char type = cmd & 0xF0;

        if (type == 0x90 || type == 0x80) {
            // Note event
            unsigned char note = (track->message >> 8) & 0xFF;
            unsigned char velocity = (track->message >> 16) & 0xFF;

            // Add to preprocessed events
            if (player->numPreprocessedEvents < MAX_PREPROCESSED_NOTES) {
                PreprocessedEvent* event = &player->preprocessedEvents[player->numPreprocessedEvents];
                event->tick = track->tick;
                event->channel = channel;
                event->note = note;
                event->trackIndex = earliestTrack;
                event->tempo = 0;  // Not used for note events
                event->controller = 0;  // Not used for note events
                event->value = 0;  // Not used for note events
                event->rawMessage = track->message;

                if (type == 0x90 && velocity > 0) {
                    // Note-on event
                    event->type = 0;
                    event->velocity = velocity;
                } else {
                    // Note-off event (either 0x80 or 0x90 with velocity 0)
                    event->type = 1;
                    event->velocity = 0;
                }

                player->numPreprocessedEvents++;
            }
        } else if (type == 0xB0) {
            // Controller event (includes modulation, sustain pedal, etc.)
            unsigned char controller = (track->message >> 8) & 0xFF;
            unsigned char value = (track->message >> 16) & 0xFF;

            // Add to preprocessed events
            if (player->numPreprocessedEvents < MAX_PREPROCESSED_NOTES) {
                PreprocessedEvent* event = &player->preprocessedEvents[player->numPreprocessedEvents];
                event->tick = track->tick;
                event->type = 3;  // Controller event
                event->channel = channel;
                event->note = 0;  // Not used for controller events
                event->velocity = 0;  // Not used for controller events
                event->trackIndex = earliestTrack;
                event->tempo = 0;  // Not used for controller events
                event->controller = controller;
                event->value = value;
                event->rawMessage = track->message;

                player->numPreprocessedEvents++;
            }
        } else if (type == 0xE0) {
            // Pitch bend event
            // Add to preprocessed events
            if (player->numPreprocessedEvents < MAX_PREPROCESSED_NOTES) {
                PreprocessedEvent* event = &player->preprocessedEvents[player->numPreprocessedEvents];
                event->tick = track->tick;
                event->type = 3;  // Treat as controller event for simplicity
                event->channel = channel;
                event->note = 0;  // Not used for pitch bend
                event->velocity = 0;  // Not used for pitch bend
                event->trackIndex = earliestTrack;
                event->tempo = 0;  // Not used for pitch bend
                event->controller = 0xE0;  // Special marker for pitch bend
                event->value = 0;  // Not used directly
                event->rawMessage = track->message;  // Store the raw message

                player->numPreprocessedEvents++;
            }
        } else if (cmd == 0xFF) {
            // Meta event
            unsigned char metaType = (track->message >> 8) & 0xFF;

            // Handle tempo changes
            if (metaType == 0x51 && track->longMsgLen >= 3) {
                int tempo = (track->longMsg[0] << 16) | (track->longMsg[1] << 8) | track->longMsg[2];

                // Add tempo change to preprocessed events
                if (tempo > 0 && player->numPreprocessedEvents < MAX_PREPROCESSED_NOTES) {
                    PreprocessedEvent* event = &player->preprocessedEvents[player->numPreprocessedEvents];
                    event->tick = track->tick;
                    event->type = 2;  // Tempo change
                    event->channel = 0;  // Not used for tempo
                    event->note = 0;     // Not used for tempo
                    event->velocity = 0;  // Not used for tempo
                    event->trackIndex = earliestTrack;
                    event->tempo = tempo;
                    event->controller = 0;  // Not used for tempo
                    event->value = 0;  // Not used for tempo
                    event->rawMessage = 0;  // Not used for tempo

                    player->numPreprocessedEvents++;
                }
            }
        } else if (cmd < 0xF0) {
            // Other MIDI channel messages (program change, aftertouch, etc.)
            // Add to preprocessed events
            if (player->numPreprocessedEvents < MAX_PREPROCESSED_NOTES) {
                PreprocessedEvent* event = &player->preprocessedEvents[player->numPreprocessedEvents];
                event->tick = track->tick;
                event->type = 3;  // Treat as controller event for simplicity
                event->channel = channel;
                event->note = 0;
                event->velocity = 0;
                event->trackIndex = earliestTrack;
                event->tempo = 0;
                event->controller = type;  // Use the message type as controller
                event->value = 0;
                event->rawMessage = track->message;  // Store the raw message

                player->numPreprocessedEvents++;
            }
        }

        // Move to next event in this track
        if (track->active) {
            TrackUpdateTick(track);

            // If track is still active, add it back to the heap
            if (track->active && track->offset < track->length) {
                // Add to heap
                int pos = heapSize++;
                trackHeap[pos] = earliestTrack;

                // Bubble up
                while (pos > 0) {
                    int parent = PARENT(pos);
                    if (player->tracks[trackHeap[pos]].tick < player->tracks[trackHeap[parent]].tick) {
                        // Swap
                        int temp = trackHeap[pos];
                        trackHeap[pos] = trackHeap[parent];
                        trackHeap[parent] = temp;
                        pos = parent;
                    } else {
                        break;
                    }
                }
            }
        }

        // Print progress every 1000 events
        if (player->eventsProcessed % 1000 == 0) {
            float progress = (float)player->eventsProcessed / player->totalEventsToProcess;
            //printf("Preprocessing progress: %.1f%% (%d/%d events)\n",
                   //progress * 100.0f, player->eventsProcessed, player->totalEventsToProcess);
        }

        // Print detailed event info every 100000 events
        if (player->eventsProcessed % 1000000 == 0 && player->eventsProcessed > 0) {
            printf("\nLive preprocessed events update (last 10 events):\n");
            int startIdx = player->numPreprocessedEvents > 10 ? player->numPreprocessedEvents - 10 : 0;
            for (int i = startIdx; i < player->numPreprocessedEvents; i++) {
                PreprocessedEvent* event = &player->preprocessedEvents[i];
                printf("Event %d: Tick=%d, ", i, event->tick);

                switch (event->type) {
                    case 0:
                        printf("Note-On, Channel=%d, Note=%d, Velocity=%d\n",
                               event->channel, event->note, event->velocity);
                        break;
                    case 1:
                        printf("Note-Off, Channel=%d, Note=%d\n",
                               event->channel, event->note);
                        break;
                    case 2:
                        printf("Tempo Change, BPM=%d\n",
                               60000000 / event->tempo);
                        break;
                    case 3:
                        if (event->controller == 0xE0) {
                            printf("Pitch Bend, Channel=%d\n", event->channel);
                        } else {
                            printf("Controller, Channel=%d, Controller=%d, Value=%d\n",
                                   event->channel, event->controller, event->value);
                        }
                        break;
                    default:
                        printf("Unknown event type\n");
                }
            }
            printf("\n");
        }
    }

    // Sort the preprocessed events by tick time
    qsort(player->preprocessedEvents, player->numPreprocessedEvents, sizeof(PreprocessedEvent), ComparePreprocessedEvents);

    // Free the heap memory
    free(trackHeap);

    // Reset all tracks to beginning
    for (int i = 0; i < player->numTracks; i++) {
        MidiTrack* track = &player->tracks[i];
        track->tick = 0;
        track->offset = 0;
        track->active = true;

        // Read the first delta time
        TrackUpdateTick(track);
    }

    // Restore original state
    player->tick = originalTick;
    player->playTime = originalPlayTime;
    player->currentEventIndex = 0;
    player->usePreprocessedEvents = true;
    player->isPreprocessing = false;

    printf("Preprocessing complete. Found %d events.\n", player->numPreprocessedEvents);
}

// Function to process preprocessed events - optimized for speed
void ProcessPreprocessedEvents(MidiPlayer* player) {
    // Early exit if no events to process
    if (player->currentEventIndex >= player->numPreprocessedEvents)
        return;

    // Cache frequently accessed values to reduce pointer dereferencing
    const int currentTick = player->tick;
    const float currentPlayTime = player->playTime;
    const bool audioEnabled = player->kdmapiInitialized;
    const int maxEvents = player->numPreprocessedEvents;
    PreprocessedEvent* events = player->preprocessedEvents;
    NoteState* notes = player->notes;
    int eventIndex = player->currentEventIndex;

    // Process events in batches for better cache locality
    const int BATCH_SIZE = 32;  // Adjust based on CPU cache size

    // Process all events that should occur up to the current tick
    while (eventIndex < maxEvents && events[eventIndex].tick <= currentTick) {
        // Process a batch of events
        int batchEnd = eventIndex + BATCH_SIZE;
        if (batchEnd > maxEvents) batchEnd = maxEvents;

        // Pre-check if we need to break out of the batch
        if (batchEnd < maxEvents && events[batchEnd].tick > currentTick) {
            // Find the last event in this tick
            while (batchEnd > eventIndex && events[batchEnd-1].tick > currentTick) {
                batchEnd--;
            }
        }

        // Process the batch
        for (; eventIndex < batchEnd && events[eventIndex].tick <= currentTick; eventIndex++) {
            PreprocessedEvent* event = &events[eventIndex];

            switch (event->type) {
                case 0: {  // Note-on event
                    // Direct array access is faster than pointer dereferencing
                    int noteIdx = event->note;
                    notes[noteIdx].active = true;
                    notes[noteIdx].startTime = currentPlayTime;
                    notes[noteIdx].releaseTime = -1;  // Not released yet
                    notes[noteIdx].channel = event->channel;
                    notes[noteIdx].note = noteIdx;
                    notes[noteIdx].velocity = event->velocity;
                    notes[noteIdx].trackIndex = event->trackIndex;

                    if (audioEnabled) {
                        SendMidiNoteOn(event->channel, noteIdx, event->velocity);
                    }
                    break;
                }

                case 1: {  // Note-off event
                    int noteIdx = event->note;
                    if (notes[noteIdx].active) {
                        notes[noteIdx].active = false;
                        notes[noteIdx].releaseTime = currentPlayTime;

                        if (audioEnabled) {
                            SendMidiNoteOff(event->channel, noteIdx);
                        }
                    }
                    break;
                }

                case 2: {  // Tempo change event
                    if (event->tempo > 0) {
                        // Store the time and tick at this tempo change
                        player->lastTempoTick = currentTick;
                        player->lastTempoTime = currentPlayTime;

                        // Update the tempo
                        player->currentTempo = event->tempo;
                        player->bpm = 60000000 / event->tempo;
                        player->ticksPerSecond = (1000000.0f / event->tempo) * player->timeDivision;
                    }
                    break;
                }

                case 3: {  // Controller or other MIDI event
                    if (audioEnabled) {
                        SendDirectData(event->rawMessage);
                    }
                    break;
                }
            }
        }
    }

    // Update the player's event index
    player->currentEventIndex = eventIndex;
}

// Optimized event processing thread
void* EventProcessingThread(void* userData) {
    MidiPlayer* player = (MidiPlayer*)userData;

    // Target update rate (microseconds) - set to 0 for unlimited speed
    const int TARGET_UPDATE_INTERVAL = 0;  // 0 = unlimited updates per second

    // No timing structures needed for unlimited performance

    // Performance tracking
    int updateCount = 0;
    double lastPerformanceCheckTime = GetTime();

    while (player->continueProcessing) {
        double loopStartTime = GetTime();

        pthread_mutex_lock(&player->playerMutex);
        float currentTime = loopStartTime;  // Use already calculated time
        float deltaTime = currentTime - player->lastEventProcessTime;

        if (!player->isPaused) {
            // Update player time
            player->playTime += deltaTime;

            // Calculate current tick position accounting for tempo changes
            float elapsedTime = player->playTime - player->lastTempoTime;
            int ticksElapsed = (int)(elapsedTime * player->ticksPerSecond);
            player->tick = player->lastTempoTick + ticksElapsed;

            if (player->usePreprocessedEvents) {
                // Process preprocessed events only - much faster
                ProcessPreprocessedEvents(player);
            } else {
                // Process tracks normally (original code)
                for (int i = 0; i < player->numTracks; i++) {
                    MidiTrack* track = &player->tracks[i];

                    if (!track->active) continue;

                    while (track->active && track->tick <= player->tick && track->offset < track->length) {
                        TrackUpdateCmd(track);
                        TrackUpdateMsg(track, player);
                        TrackExecuteCmd(track, player);

                        if (track->active) {
                            TrackUpdateTick(track);
                        }
                    }
                }
            }
        }

        player->lastEventProcessTime = currentTime;
        pthread_mutex_unlock(&player->playerMutex);

        // Adaptive sleep to maintain target update rate
        updateCount++;
        double loopEndTime = GetTime();
        double loopDuration = (loopEndTime - loopStartTime) * 1000000;  // Convert to microseconds

        // Absolutely no sleep or yield - run at maximum possible speed
        // This will use 100% of a CPU core but provide maximum performance

        // Periodically log performance stats (but don't adjust timing in unlimited mode)
        double now = GetTime();
        if (now - lastPerformanceCheckTime > 1.0) {  // Check every second
            double eventsPerSecond = updateCount / (now - lastPerformanceCheckTime);
            updateCount = 0;
            lastPerformanceCheckTime = now;

            // Optionally log performance stats
            // printf("Event processing rate: %.0f events/sec\n", eventsPerSecond);
        }
    }

    return NULL;
}


int TrackDecodeVarLen(MidiTrack* track) {
    int value = 0;
    unsigned char c;

    do {
        c = track->data[track->offset];
        value = (value << 7) + (c & 0x7F);
        track->offset++;
    } while (c >= 0x80);

    return value;
}

void TrackUpdateTick(MidiTrack* track) {
    track->tick += TrackDecodeVarLen(track);
}

void TrackUpdateCmd(MidiTrack* track) {
    track->tmp = track->data[track->offset];

    if (track->tmp >= 0x80) {
        track->offset++;
        track->message = track->tmp;
    } else {
        track->message = track->message & 0xFF;
    }
}

void TrackUpdateMsg(MidiTrack* track, MidiPlayer* player) {
    unsigned char cmd = track->message & 0xFF;

    if (cmd < 0xC0) {
        track->tmp = track->data[track->offset] << 8;
        track->tmp |= track->data[track->offset + 1] << 16;
        track->offset += 2;
    } else if (cmd < 0xE0) {
        track->tmp = track->data[track->offset] << 8;
        track->offset += 1;
    } else if (cmd < 0xF0) {
        track->tmp = track->data[track->offset] << 8;
        track->tmp |= track->data[track->offset + 1] << 16;
        track->offset += 2;
    } else if (cmd == 0xFF) {
        track->tmp = track->data[track->offset] << 8;
        track->offset++;
        track->longMsgLen = TrackDecodeVarLen(track);

        if (track->longMsg != NULL) {
            free(track->longMsg);
        }
        track->longMsg = (unsigned char*)malloc(track->longMsgLen);

        memcpy(track->longMsg, &track->data[track->offset], track->longMsgLen);
        track->offset += track->longMsgLen;
    } else if (cmd == 0xF0) {
        track->tmp = 0;
        track->longMsgLen = TrackDecodeVarLen(track);

        if (track->longMsg != NULL) {
            free(track->longMsg);
        }
        track->longMsg = (unsigned char*)malloc(track->longMsgLen);

        memcpy(track->longMsg, &track->data[track->offset], track->longMsgLen);
        track->offset += track->longMsgLen;
    }

    track->message |= track->tmp;
}

void SendMidiNoteOn(int channel, int note, int velocity) {
    unsigned long msg = 0x90 | (channel & 0x0F) | (note << 8) | (velocity << 16);
    SendDirectData(msg);
}

void SendMidiNoteOff(int channel, int note) {
    unsigned long msg = 0x80 | (channel & 0x0F) | (note << 8);
    SendDirectData(msg);
}

Color DarkenColor(Color color, float amount) {
    return (Color){
        (unsigned char)(color.r * amount),
        (unsigned char)(color.g * amount),
        (unsigned char)(color.b * amount),
        color.a
    };
}

void TrackExecuteCmd(MidiTrack* track, MidiPlayer* player) {
    unsigned char cmd = track->message & 0xFF;
    unsigned char channel = cmd & 0x0F;
    unsigned char type = cmd & 0xF0;
    int trackIndex = 0;

    // Find track index for coloring
    for (int i = 0; i < player->numTracks; i++) {
        if (&player->tracks[i] == track) {
            trackIndex = i;
            break;
        }
    }

    if (cmd < 0xF0) {
		// In TrackExecuteCmd function, where you handle note-on events (0x90)
		if (type == 0x90) {
			unsigned char note = (track->message >> 8) & 0xFF;
			unsigned char velocity = (track->message >> 16) & 0xFF;

			if (velocity > 0) {
				// This is a Note-On event
				player->notes[note].active = true;
				player->notes[note].startTime = player->playTime;
				player->notes[note].releaseTime = -1;  // Not released yet
				player->notes[note].channel = channel;
				player->notes[note].note = note;
				player->notes[note].velocity = velocity;
				player->notes[note].trackIndex = trackIndex;

				if (player->kdmapiInitialized) {
					SendMidiNoteOn(channel, note, velocity);
				}
			} else {
				// This is actually a Note-Off event (note-on with velocity 0)
				if (player->notes[note].active) {
					player->notes[note].active = false;
					player->notes[note].releaseTime = player->playTime;  // Record exact release time

					if (player->kdmapiInitialized) {
						SendMidiNoteOff(channel, note);
					}
				}
			}
		} else if (type == 0x80) {
			// This is a Note-Off event
			unsigned char note = (track->message >> 8) & 0xFF;
			if (player->notes[note].active) {
				player->notes[note].active = false;
				player->notes[note].releaseTime = player->playTime;  // Record exact release time

				if (player->kdmapiInitialized) {
					SendMidiNoteOff(channel, note);
				}
			}
		} else {
            if (player->kdmapiInitialized) {
                SendDirectData(track->message);
            }
        }
    } else {
        if (cmd == 0xFF) {
            unsigned char metaType = (track->message >> 8) & 0xFF;

			// In TrackExecuteCmd function, modify the tempo handling:
			if (metaType == 0x51) {
				if (track->longMsgLen >= 3) {
					int tempo = (track->longMsg[0] << 16) | (track->longMsg[1] << 8) | track->longMsg[2];

					// Prevent division by zero
					if (tempo > 0) {
						// Store the time and tick at this tempo change
						player->lastTempoTick = player->tick;
						player->lastTempoTime = player->playTime;

						// Update the tempo
						player->currentTempo = tempo;
						player->bpm = 60000000 / tempo;
						player->ticksPerSecond = (1000000.0f / tempo) * player->timeDivision;
					} else {
						printf("Warning: Invalid tempo value (0) encountered, ignoring.\n");
					}
				} else {
					printf("Warning: Invalid tempo meta event (too short), ignoring.\n");
				}
			}
        }
    }
}

MidiPlayer* LoadMidiFile(const char* filename) {
    FILE* file = fopen(filename, "rb");
    if (!file) {
        printf("Failed to open MIDI file: %s\n", filename);
        return NULL;
    }

    char header[5] = {0};
    fread(header, 1, 4, file);
    if (strcmp(header, "MThd") != 0) {
        printf("Not a valid MIDI file: %s\n", filename);
        fclose(file);
        return NULL;
    }

    unsigned int headerLength;
    fread(&headerLength, 4, 1, file);
    headerLength = ((headerLength & 0xFF) << 24) | ((headerLength & 0xFF00) << 8) |
                   ((headerLength & 0xFF0000) >> 8) | ((headerLength & 0xFF000000) >> 24);
    if (headerLength != 6) {
        printf("Invalid header length in MIDI file\n");
        fclose(file);
        return NULL;
    }

    unsigned short format;
    fread(&format, 2, 1, file);
    format = ((format & 0xFF) << 8) | ((format & 0xFF00) >> 8);
    if (format > 1) {
        printf("Unsupported MIDI format: %d\n", format);
        fclose(file);
        return NULL;
    }

    unsigned short numTracks, timeDivision;
    fread(&numTracks, 2, 1, file);
    fread(&timeDivision, 2, 1, file);
    numTracks = ((numTracks & 0xFF) << 8) | ((numTracks & 0xFF00) >> 8);
    timeDivision = ((timeDivision & 0xFF) << 8) | ((timeDivision & 0xFF00) >> 8);

    if (timeDivision >= 0x8000) {
        printf("SMPTE timing not supported\n");
        fclose(file);
        return NULL;
    }

    printf("Loading %d tracks with time division %d\n", numTracks, timeDivision);

    // Seed random for track colors
    srand(time(NULL));

    MidiPlayer* player = (MidiPlayer*)malloc(sizeof(MidiPlayer));
	player->lastEventProcessTime = GetTime();
    player->continueProcessing = false;  // Will be set to true when starting thread
    pthread_mutex_init(&player->playerMutex, NULL);
    player->tick = 0;
    player->multi = 1.0f;
    player->bpm = 120;
	player->currentTempo = 60000000.0f / player->bpm;  // 500,000 microseconds per quarter note at 120 BPM
	player->lastTempoTick = 0;
	player->lastTempoTime = 0.0f;
    player->deltaTick = 0;
    player->numTracks = numTracks;
    player->timeDivision = timeDivision;
    player->playTime = 0.0f;
	player->useChannelColors = false;
    player->kdmapiInitialized = false;
    player->isPaused = true;
    player->pixelsPerSecond = WINDOW_WIDTH / SECONDS_BUFFER;
    player->textureScrollPos = 0.0f;
    player->lastUpdateTime = 0.0f;

    // Initialize preprocessed events
    player->preprocessedEvents = NULL;
    player->numPreprocessedEvents = 0;
    player->currentEventIndex = 0;
    player->usePreprocessedEvents = false;

    // Initialize preprocessing progress
    player->totalEventsToProcess = 0;
    player->eventsProcessed = 0;
    player->isPreprocessing = false;

    // Initialize notes array
    for (int i = 0; i < MAX_MIDI_NOTES; i++) {
        player->notes[i].active = false;
        player->notes[i].startTime = 0.0f;
        player->notes[i].releaseTime = 0.0f;
        player->notes[i].channel = 0;
        player->notes[i].note = i;
        player->notes[i].velocity = 0;
        player->notes[i].trackIndex = 0;
    }

    // Initialize the texture
    InitializeNoteTexture(player);

    float tempo = 60000000.0f / player->bpm;
    player->ticksPerSecond = (1000000.0f / tempo) * timeDivision;

    player->tracks = (MidiTrack*)malloc(sizeof(MidiTrack) * numTracks);

    for (int i = 0; i < numTracks; i++) {
        char trackHeader[5] = {0};
        fread(trackHeader, 1, 4, file);
        if (strcmp(trackHeader, "MTrk") != 0) {
            printf("Invalid track header in track %d\n", i);
            CleanupMidiPlayer(player);
            fclose(file);
            return NULL;
        }

        unsigned int trackLength;
        fread(&trackLength, 4, 1, file);
        trackLength = ((trackLength & 0xFF) << 24) | ((trackLength & 0xFF00) << 8) |
                      ((trackLength & 0xFF0000) >> 8) | ((trackLength & 0xFF000000) >> 24);

        player->tracks[i].data = (unsigned char*)malloc(trackLength);
        player->tracks[i].tick = 0;
        player->tracks[i].offset = 0;
        player->tracks[i].length = trackLength;
        player->tracks[i].message = 0;
        player->tracks[i].tmp = 0;
        player->tracks[i].longMsgLen = 0;
        player->tracks[i].longMsg = NULL;
        player->tracks[i].active = true;


		for (int i = 0; i < 16; i++) {
			player->channelColors[i] = (Color){
				rand() % 256,
				rand() % 256,
				rand() % 256,
				255
			};
			player->channelOutlineColors[i] = DarkenColor(player->channelColors[i], 0.35f);
		}
		//–– fully random 0–255 RGB ––
		player->tracks[i].color = (Color){
			rand() % 256,
			rand() % 256,
			rand() % 256,
			255
		};
		player->tracks[i].outlineColor = DarkenColor(player->tracks[i].color, 0.35f);


        fread(player->tracks[i].data, 1, trackLength, file);

        TrackUpdateTick(&player->tracks[i]);
    }

    fclose(file);

    // Preprocess all MIDI events
    PreprocessAllEvents(player);

    return player;
}

void InitializeNoteTexture(MidiPlayer* player) {
    // Create the render texture for notes - twice as wide as the window
    player->noteTexture = LoadRenderTexture(WINDOW_WIDTH * 1, TEXTURE_HEIGHT);

    // Clear the texture
    BeginTextureMode(player->noteTexture);
    ClearBackground(BLANK);
    EndTextureMode();
}

// Optimized note texture update function
void UpdateNoteTexture(MidiPlayer* player) {
    // Calculate how much time has passed since last update
    float timeDelta = player->playTime - player->lastUpdateTime;
    float pixelsDelta = timeDelta * player->pixelsPerSecond;

    // Skip update if delta is too small (less than 1 pixel)
    if (pixelsDelta < 1.0f) {
        return;
    }

    // Begin drawing to texture
    BeginTextureMode(player->noteTexture);

    // Clear the area ahead where new notes will be drawn
    DrawRectangle(player->textureScrollPos, 0, pixelsDelta + 1, TEXTURE_HEIGHT, GRAY);

    // Pre-calculate constants
    const float currentPlayTime = player->playTime;
    const float pixelsPerSecond = player->pixelsPerSecond;
    const float scrollPos = player->textureScrollPos;
    const bool useChannelColors = player->useChannelColors;
    const int noteHeight = 4;

    // Create a list of visible notes to avoid processing all notes
    typedef struct {
        int noteIndex;
        int trackIndex;
    } VisibleNote;

    VisibleNote visibleNotes[MAX_MIDI_NOTES];
    int visibleNoteCount = 0;

    // First pass: collect all visible notes
    for (int i = 0; i < MAX_MIDI_NOTES; i++) {
        NoteState* note = &player->notes[i];

        // Skip if note is not active and not recently released
        if (!note->active && note->releaseTime <= 0) continue;

        // Calculate note position
        float startX = (note->startTime - currentPlayTime) * pixelsPerSecond + scrollPos;
        float endX;

        if (note->active) {
            // Note is still active, extend to current time
            endX = scrollPos;
        } else {
            // Note has been released
            endX = (note->releaseTime - currentPlayTime) * pixelsPerSecond + scrollPos;
        }

        // Only include if visible in the current window
        if (startX < WINDOW_WIDTH * 2 && endX > 0) {
            visibleNotes[visibleNoteCount].noteIndex = i;
            visibleNotes[visibleNoteCount].trackIndex = note->trackIndex;
            visibleNoteCount++;
        }
    }

    // Sort visible notes by track index for z-ordering
    for (int i = 0; i < visibleNoteCount - 1; i++) {
        for (int j = 0; j < visibleNoteCount - i - 1; j++) {
            if (visibleNotes[j].trackIndex > visibleNotes[j + 1].trackIndex) {
                VisibleNote temp = visibleNotes[j];
                visibleNotes[j] = visibleNotes[j + 1];
                visibleNotes[j + 1] = temp;
            }
        }
    }

    // Process visible notes by track
    int currentTrack = -1;

    // Lookup tables for gap filling
    float prevNoteEndTimes[MAX_MIDI_NOTES];
    bool hasPrevNote[MAX_MIDI_NOTES];

    for (int i = 0; i < visibleNoteCount; i++) {
        int noteIdx = visibleNotes[i].noteIndex;
        int trackIdx = visibleNotes[i].trackIndex;

        // If we've moved to a new track, reset the lookup tables
        if (trackIdx != currentTrack) {
            currentTrack = trackIdx;

            // Reset lookup tables
            for (int n = 0; n < MAX_MIDI_NOTES; n++) {
                prevNoteEndTimes[n] = -1;
                hasPrevNote[n] = false;
            }

            // First pass for this track: find end times
            for (int j = i; j < visibleNoteCount && visibleNotes[j].trackIndex == trackIdx; j++) {
                NoteState* note = &player->notes[visibleNotes[j].noteIndex];

                // Record the end time for this note
                float endTime = note->active ? currentPlayTime : note->releaseTime;

                // Update the lookup table if this is a later end time
                if (!hasPrevNote[note->note] || endTime > prevNoteEndTimes[note->note]) {
                    prevNoteEndTimes[note->note] = endTime;
                    hasPrevNote[note->note] = true;
                }
            }
        }

        // Process this note
        NoteState* note = &player->notes[noteIdx];

        // Calculate note position
        float startX = (note->startTime - currentPlayTime) * pixelsPerSecond + scrollPos;
        float endX;

        if (note->active) {
            // Note is still active, extend to current time
            endX = scrollPos;
        } else {
            // Note has been released
            endX = (note->releaseTime - currentPlayTime) * pixelsPerSecond + scrollPos;
        }

        // Check for gaps with a more efficient approach
        // Only check notes that are visible and in the same track
        for (int j = i + 1; j < visibleNoteCount && visibleNotes[j].trackIndex == trackIdx; j++) {
            NoteState* nextNote = &player->notes[visibleNotes[j].noteIndex];

            // Skip if not same note value
            if (nextNote->note != note->note) continue;

            // Check if this is a consecutive note (starts right after current one ends)
            if (fabsf(nextNote->startTime - note->releaseTime) < 0.01f) {
                // Extend the current note to meet the next one
                endX = (nextNote->startTime - currentPlayTime) * pixelsPerSecond + scrollPos;
                break;  // Only need to find the first consecutive note
            }
        }

        // Calculate note height and position
        int yPos = TEXTURE_HEIGHT - ((note->note + 1) * noteHeight);

        // Get the track color for this note
        Color noteColor, outlineColor;
        if (useChannelColors) {
            noteColor = player->channelColors[note->channel];
            outlineColor = player->channelOutlineColors[note->channel];
        } else {
            noteColor = player->tracks[note->trackIndex].color;
            outlineColor = player->tracks[note->trackIndex].outlineColor;
        }

        // Draw the note with outline - ensure width is at least 1 pixel
        float width = fmaxf(endX - startX, 1.0f);
        DrawRectangle(startX - 1, yPos - 1, width + 2, noteHeight + 2, outlineColor);
        DrawRectangle(startX, yPos, width, noteHeight, noteColor);
    }

    EndTextureMode();

    // Update scroll position
    player->textureScrollPos += pixelsDelta;

    // If we've scrolled completely past the window, reset the scroll position
    if (player->textureScrollPos >= WINDOW_WIDTH) {
        player->textureScrollPos -= WINDOW_WIDTH;
    }

    // Update last update time
    player->lastUpdateTime = player->playTime;
}

void DrawNoteTexture(MidiPlayer* player) {
    // Draw the texture with rotation
    // Set rotation center point (middle of the screen)
    Vector2 center = { WINDOW_WIDTH / 2.0f, WINDOW_HEIGHT / 2.0f };

    // Begin 2D mode with custom camera to handle rotation
    Camera2D camera = { 0 };
    camera.offset = center;
    camera.target = (Vector2){ 0, 0 };
    camera.rotation = 270.0f;  // Rotate 90 degrees
    camera.zoom = 1.775f;

    BeginMode2D(camera);

    // Draw the first part of the texture
    Rectangle source1 = {player->textureScrollPos, 0, WINDOW_WIDTH - player->textureScrollPos, TEXTURE_HEIGHT};
    Rectangle dest1 = {-center.x, -center.y, WINDOW_WIDTH - player->textureScrollPos, WINDOW_HEIGHT};
    DrawTexturePro(player->noteTexture.texture, source1, dest1, (Vector2){0, 0}, 0.0f, WHITE);

    // If we're close to wrapping, draw the beginning of the texture at the right edge
    if (player->textureScrollPos > 0) {
        Rectangle source2 = {0, 0, player->textureScrollPos, TEXTURE_HEIGHT};
        Rectangle dest2 = {-center.x + (WINDOW_WIDTH - player->textureScrollPos), -center.y, player->textureScrollPos, WINDOW_HEIGHT};
        DrawTexturePro(player->noteTexture.texture, source2, dest2, (Vector2){0, 0}, 0.0f, WHITE);
    }

    EndMode2D();
}

bool InitializeAudio(MidiPlayer* player) {
    if (IsKDMAPIAvailable()) {
        printf("KDMAPI is available, initializing...\n");
        InitializeKDMAPIStream();
        ResetKDMAPIStream();
        player->kdmapiInitialized = true;
        printf("KDMAPI initialized successfully\n");
        return true;
    } else {
        printf("KDMAPI is not available\n");
        player->kdmapiInitialized = false;
        return false;
    }
}

void ReleaseAllNotes(MidiPlayer* player) {
    if (!player->kdmapiInitialized) return;

    for (int i = 0; i < MAX_MIDI_NOTES; i++) {
        if (player->notes[i].active) {
            SendMidiNoteOff(player->notes[i].channel, i);
            player->notes[i].active = false;
            player->notes[i].releaseTime = player->playTime;
        }
    }

    for (int channel = 0; channel < 16; channel++) {
        unsigned long msg = 0xB0 | channel | (123 << 8) | (0 << 16);
        SendDirectData(msg);
    }
}

void ShutdownAudio(MidiPlayer* player) {
    if (player->kdmapiInitialized) {
        for (int channel = 0; channel < 16; channel++) {
            unsigned long msg = 0xB0 | channel | (123 << 8) | (0 << 16);
            SendDirectData(msg);
        }

        int result = TerminateKDMAPIStream();
        printf("KDMAPI terminated with result: %d\n", result);

        player->kdmapiInitialized = false;
    }
}

bool UpdateMidiPlayer(MidiPlayer* player, float deltaTime) {
    if (player->isPaused) return true;

    // We only need to update the texture here
    // The event processing is done in the thread
    pthread_mutex_lock(&player->playerMutex);

    // Update texture with safety check
    if (player->lastUpdateTime <= player->playTime) {
        UpdateNoteTexture(player);
    }

    // Check if any tracks are still active
    bool anyActive = false;
    for (int i = 0; i < player->numTracks; i++) {
        if (player->tracks[i].active) {
            anyActive = true;
            break;
        }
    }

    pthread_mutex_unlock(&player->playerMutex);
    return anyActive;
}

// Add function to start event processing thread
void StartEventProcessing(MidiPlayer* player) {
    player->continueProcessing = true;
    pthread_create(&player->processingThread, NULL, EventProcessingThread, player);
}

// Add function to stop event processing thread
void StopEventProcessing(MidiPlayer* player) {
    player->continueProcessing = false;
    pthread_join(player->processingThread, NULL);
}

// Modify CleanupMidiPlayer to clean up thread resources
void CleanupMidiPlayer(MidiPlayer* player) {
    if (player) {
        StopEventProcessing(player);
        pthread_mutex_destroy(&player->playerMutex);
        ShutdownAudio(player);

        // Unload note texture
        UnloadRenderTexture(player->noteTexture);

        // Free preprocessed events
        if (player->preprocessedEvents) {
            free(player->preprocessedEvents);
            player->preprocessedEvents = NULL;
        }

        for (int i = 0; i < player->numTracks; i++) {
            if (player->tracks[i].data) {
                free(player->tracks[i].data);
            }
            if (player->tracks[i].longMsg) {
                free(player->tracks[i].longMsg);
            }
        }
        free(player->tracks);
        free(player);
    }
}

int main(int argc, char** argv) {
    if (argc < 2) {
        printf("Usage: %s <midi_file>\n", argv[0]);
        return 1;
    }

    // Initialize window with absolutely no FPS limitations and fixed size
    // No flags needed - default is non-resizable window with no vsync
    InitWindow(WINDOW_WIDTH, WINDOW_HEIGHT, "MIDI Player");
    SetTargetFPS(0);  // Unlimited FPS - no frame rate cap

    MidiPlayer* player = LoadMidiFile(argv[1]);
    if (!player) {
        printf("Failed to load MIDI file\n");
        CloseWindow();
        return 1;
    }

    if (!InitializeAudio(player)) {
        printf("Warning: Audio output not available. Continuing with visual only.\n");
    }

    // Start the event processing thread
    StartEventProcessing(player);

    const char* filename = GetFileName(argv[1]);

    // Performance tracking - for information only, no limiting
    double lastRenderTime = GetTime();
    double frameTime = 0;
    int frameCount = 0;
    double totalFrameTime = 0;
    double highestFPS = 0;

    bool playing = true;
    while (!WindowShouldClose() && playing) {
        double frameStartTime = GetTime();
        float deltaTime = (float)(frameStartTime - lastRenderTime);
        lastRenderTime = frameStartTime;

        // Handle user input with mutex protection
        pthread_mutex_lock(&player->playerMutex);

        if (IsKeyPressed(KEY_C)) {
            player->useChannelColors = !player->useChannelColors;
        }

        if (IsKeyPressed(KEY_P)) {
            player->usePreprocessedEvents = !player->usePreprocessedEvents;
            printf("Preprocessed mode: %s\n", player->usePreprocessedEvents ? "ON" : "OFF");
        }

        if (IsKeyPressed(KEY_SPACE)) {
            player->isPaused = !player->isPaused;

            if (player->isPaused) {
                ReleaseAllNotes(player);
            }
        }

        if (IsKeyPressed(KEY_R)) {
            // Reset implementation with mutex protection
            player->tick = 0;
            player->playTime = 0;
            player->lastUpdateTime = 0;
            player->textureScrollPos = 0;
            player->lastTempoTick = 0;
            player->lastTempoTime = 0.0f;
            player->currentTempo = 60000000.0f / player->bpm;
            player->ticksPerSecond = (1000000.0f / player->currentTempo) * player->timeDivision;
            ReleaseAllNotes(player);

            // Reset preprocessed events index
            player->currentEventIndex = 0;

            for (int i = 0; i < player->numTracks; i++) {
                player->tracks[i].tick = 0;
                player->tracks[i].offset = 0;
                player->tracks[i].active = true;

                TrackUpdateTick(&player->tracks[i]);
            }

            // Reset all notes
            for (int i = 0; i < MAX_MIDI_NOTES; i++) {
                player->notes[i].active = false;
                player->notes[i].startTime = 0;
                player->notes[i].releaseTime = 0;
            }

            // Clear texture
            BeginTextureMode(player->noteTexture);
            ClearBackground(BLANK);
            EndTextureMode();
        }

        pthread_mutex_unlock(&player->playerMutex);

        // Update visual representation only
        playing = UpdateMidiPlayer(player, deltaTime);

        if (!playing) {
            pthread_mutex_lock(&player->playerMutex);
            ReleaseAllNotes(player);
            pthread_mutex_unlock(&player->playerMutex);
        }

        // Rendering
        BeginDrawing();
        ClearBackground(GRAY);

        // Draw the note visualization
        if (!player->isPreprocessing) {
            DrawNoteTexture(player);
        }

        DrawText(TextFormat("MIDI Player - %s", filename), 10, 10, 20, RAYWHITE);

        if (player->isPreprocessing) {
            // Show preprocessing progress
            float progress = (float)player->eventsProcessed / player->totalEventsToProcess;
            DrawText("Preprocessing MIDI events...", 10, 35, 16, RAYWHITE);
            DrawText(TextFormat("Progress: %.1f%% (%d/%d events)",
                     progress * 100.0f, player->eventsProcessed, player->totalEventsToProcess),
                     10, 55, 16, RAYWHITE);

            // Draw progress bar
            DrawRectangle(10, 75, WINDOW_WIDTH - 20, 20, DARKGRAY);
            DrawRectangle(10, 75, (int)((WINDOW_WIDTH - 20) * progress), 20, GREEN);
        } else {
            // Show normal playback info
            DrawText(TextFormat("BPM: %d", player->bpm), 10, 35, 16, RAYWHITE);
            DrawText(TextFormat("Time: %.2f", player->playTime), 10, 55, 16, RAYWHITE);
            DrawText(TextFormat("Audio: %s", player->kdmapiInitialized ? "ON" : "OFF"), 10, 75, 16, RAYWHITE);
            DrawText(TextFormat("%s - Space to toggle, R to restart", player->isPaused ? "PAUSED" : "PLAYING"),
                     10, 95, 16, RAYWHITE);

            // Count active notes
            int activeNotes = 0;
            for (int i = 0; i < MAX_MIDI_NOTES; i++) {
                if (player->notes[i].active) activeNotes++;
            }

            DrawText(TextFormat("Active Notes: %d", activeNotes), 10, 115, 16, RAYWHITE);
            DrawText(TextFormat("Colors: %s (Press C to toggle)",
             player->useChannelColors ? "By Channel" : "By Track"),
             10, 135, 16, RAYWHITE);
            DrawText(TextFormat("Preprocessed: %s (Press P to toggle) - %d events",
             player->usePreprocessedEvents ? "ON" : "OFF", player->numPreprocessedEvents),
             10, 155, 16, RAYWHITE);

            // Show performance metrics
            frameCount++;
            double currentFrameTime = GetTime() - frameStartTime;
            totalFrameTime += currentFrameTime;

            // Update metrics every frame for true unlimited FPS display
            frameTime = totalFrameTime / frameCount;
            double currentFPS = 1.0/frameTime;

            // Track highest FPS
            if (currentFPS > highestFPS && frameCount > 10) {  // Ignore initial frames
                highestFPS = currentFPS;
            }

            // Reset counters periodically to keep measurements current
            if (frameCount > 1000) {
                frameCount = 0;
                totalFrameTime = 0;
            }

            DrawText(TextFormat("Frame Time: %.2f ms (%.0f FPS) | Highest: %.0f FPS",
                     frameTime * 1000, currentFPS, highestFPS), 10, 175, 16, RAYWHITE);
        }

        DrawFPS(
            WINDOW_WIDTH
            - MeasureText("fps: 000", 30)
            - 10,
            10
        );

        EndDrawing();
    }

    StopEventProcessing(player);
    ReleaseAllNotes(player);
    CleanupMidiPlayer(player);
    CloseWindow();

    return 0;
}