using System;
using System.Runtime.InteropServices;

namespace Kiva_MIDI
{
    /// <summary>
    /// Direct P/Invoke bindings for raylib - simplified version for basic functionality
    /// This avoids the .NET Standard compatibility issues with Raylib-cs
    /// </summary>
    public static class RaylibPInvoke
    {
        private const string RAYLIB_DLL = "raylib.dll";

        [StructLayout(LayoutKind.Sequential)]
        public struct Color
        {
            public byte r;
            public byte g;
            public byte b;
            public byte a;

            public Color(byte r, byte g, byte b, byte a)
            {
                this.r = r;
                this.g = g;
                this.b = b;
                this.a = a;
            }

            public static readonly Color BLACK = new Color(0, 0, 0, 255);
            public static readonly Color WHITE = new Color(255, 255, 255, 255);
            public static readonly Color RED = new Color(255, 0, 0, 255);
            public static readonly Color GREEN = new Color(0, 255, 0, 255);
            public static readonly Color BLUE = new Color(0, 0, 255, 255);
            public static readonly Color YELLOW = new Color(255, 255, 0, 255);
            public static readonly Color GRAY = new Color(128, 128, 128, 255);
            public static readonly Color DARKGRAY = new Color(80, 80, 80, 255);
            public static readonly Color BLANK = new Color(0, 0, 0, 0);
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Vector2
        {
            public float x;
            public float y;

            public Vector2(float x, float y)
            {
                this.x = x;
                this.y = y;
            }
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Font
        {
            public int baseSize;
            public int glyphCount;
            public int glyphPadding;
            public IntPtr texture;
            public IntPtr recs;
            public IntPtr glyphs;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Texture2D
        {
            public uint id;
            public int width;
            public int height;
            public int mipmaps;
            public int format;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct RenderTexture2D
        {
            public uint id;
            public Texture2D texture;
            public Texture2D depth;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Rectangle
        {
            public float x;
            public float y;
            public float width;
            public float height;

            public Rectangle(float x, float y, float width, float height)
            {
                this.x = x;
                this.y = y;
                this.width = width;
                this.height = height;
            }
        }

        [Flags]
        public enum ConfigFlags
        {
            FLAG_VSYNC_HINT = 0x00000040,
            FLAG_FULLSCREEN_MODE = 0x00000002,
            FLAG_WINDOW_RESIZABLE = 0x00000004,
            FLAG_WINDOW_UNDECORATED = 0x00000008,
            FLAG_WINDOW_HIDDEN = 0x00000080,
            FLAG_WINDOW_MINIMIZED = 0x00000200,
            FLAG_WINDOW_MAXIMIZED = 0x00000400,
            FLAG_WINDOW_UNFOCUSED = 0x00000800,
            FLAG_WINDOW_TOPMOST = 0x00001000,
            FLAG_WINDOW_ALWAYS_RUN = 0x00000100,
            FLAG_WINDOW_TRANSPARENT = 0x00000010,
            FLAG_WINDOW_HIGHDPI = 0x00002000,
            FLAG_MSAA_4X_HINT = 0x00000020,
            FLAG_INTERLACED_HINT = 0x00010000
        }

        // Window management
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetConfigFlags(ConfigFlags flags);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void InitWindow(int width, int height, string title);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ToggleFullscreen();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsWindowFullscreen();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMonitorWidth(int monitor);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMonitorHeight(int monitor);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool WindowShouldClose();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void CloseWindow();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsWindowReady();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetTargetFPS(int fps);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetScreenWidth();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetScreenHeight();

        // Drawing
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void BeginDrawing();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void EndDrawing();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ClearBackground(Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawRectangle(int posX, int posY, int width, int height, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawRectangleLines(int posX, int posY, int width, int height, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawLine(int startPosX, int startPosY, int endPosX, int endPosY, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawCircle(int centerX, int centerY, float radius, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawCircleLines(int centerX, int centerY, float radius, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawText(string text, int posX, int posY, int fontSize, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern Font LoadFont(string fileName);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawTextEx(Font font, string text, Vector2 position, float fontSize, float spacing, Color tint);

        // Texture loading and drawing
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern Texture2D LoadTexture(string fileName);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UnloadTexture(Texture2D texture);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawTexture(Texture2D texture, int posX, int posY, Color tint);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawTextureRec(Texture2D texture, Rectangle source, Vector2 position, Color tint);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawTexturePro(Texture2D texture, Rectangle source, Rectangle dest, Vector2 origin, float rotation, Color tint);

        // Render texture functions
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern RenderTexture2D LoadRenderTexture(int width, int height);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UnloadRenderTexture(RenderTexture2D target);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void BeginTextureMode(RenderTexture2D target);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void EndTextureMode();

        // Input
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsKeyPressed(int key);



        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsKeyDown(int key);

        // Mouse input
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonPressed(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonDown(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonReleased(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMouseX();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMouseY();

        // Key codes
        public const int KEY_ESCAPE = 256;
        public const int KEY_SPACE = 32;
        public const int KEY_ENTER = 257;
        public const int KEY_BACKSPACE = 259;
        public const int KEY_DELETE = 261;
        public const int KEY_TAB = 258;

        // Arrow keys
        public const int KEY_UP = 265;
        public const int KEY_DOWN = 264;
        public const int KEY_LEFT = 263;
        public const int KEY_RIGHT = 262;

        // Modifier keys
        public const int KEY_LEFT_SHIFT = 340;
        public const int KEY_RIGHT_SHIFT = 344;

        // Letter keys (ASCII values)
        public const int KEY_O = 79;
        public const int KEY_P = 80;
        public const int KEY_S = 83;
        public const int KEY_H = 72;
        public const int KEY_I = 73;

        // Number keys (ASCII values)
        public const int KEY_0 = 48;
        public const int KEY_1 = 49;
        public const int KEY_2 = 50;
        public const int KEY_3 = 51;
        public const int KEY_4 = 52;
        public const int KEY_5 = 53;
        public const int KEY_6 = 54;
        public const int KEY_7 = 55;
        public const int KEY_8 = 56;
        public const int KEY_9 = 57;

        // Special characters
        public const int KEY_PERIOD = 46; // '.' key

        // Mouse buttons
        public const int MOUSE_BUTTON_LEFT = 0;
        public const int MOUSE_BUTTON_RIGHT = 1;
        public const int MOUSE_BUTTON_MIDDLE = 2;

        // Helper methods for easier usage
        public static class Raylib
        {
            public static void InitWindow(int width, int height, string title) => RaylibPInvoke.InitWindow(width, height, title);
            public static bool WindowShouldClose() => RaylibPInvoke.WindowShouldClose();
            public static void CloseWindow() => RaylibPInvoke.CloseWindow();
            public static bool IsWindowReady() => RaylibPInvoke.IsWindowReady();
            public static void SetTargetFPS(int fps) => RaylibPInvoke.SetTargetFPS(fps);
            public static int GetScreenWidth() => RaylibPInvoke.GetScreenWidth();
            public static int GetScreenHeight() => RaylibPInvoke.GetScreenHeight();
            public static void BeginDrawing() => RaylibPInvoke.BeginDrawing();
            public static void EndDrawing() => RaylibPInvoke.EndDrawing();
            public static void ClearBackground(Color color) => RaylibPInvoke.ClearBackground(color);
            public static void DrawRectangle(int posX, int posY, int width, int height, Color color) => RaylibPInvoke.DrawRectangle(posX, posY, width, height, color);
            public static void DrawRectangleLines(int posX, int posY, int width, int height, Color color) => RaylibPInvoke.DrawRectangleLines(posX, posY, width, height, color);
            public static void DrawLine(int startPosX, int startPosY, int endPosX, int endPosY, Color color) => RaylibPInvoke.DrawLine(startPosX, startPosY, endPosX, endPosY, color);
            public static void DrawCircle(int centerX, int centerY, float radius, Color color) => RaylibPInvoke.DrawCircle(centerX, centerY, radius, color);
            public static void DrawCircleLines(int centerX, int centerY, float radius, Color color) => RaylibPInvoke.DrawCircleLines(centerX, centerY, radius, color);
            public static void DrawText(string text, int posX, int posY, int fontSize, Color color) => RaylibPInvoke.DrawText(text, posX, posY, fontSize, color);
            public static Font LoadFont(string fileName) => RaylibPInvoke.LoadFont(fileName);
            public static void DrawTextEx(Font font, string text, Vector2 position, float fontSize, float spacing, Color tint) => RaylibPInvoke.DrawTextEx(font, text, position, fontSize, spacing, tint);
            public static bool IsKeyPressed(int key) => RaylibPInvoke.IsKeyPressed(key);
            public static bool IsKeyDown(int key) => RaylibPInvoke.IsKeyDown(key);
            public static bool IsMouseButtonPressed(int button) => RaylibPInvoke.IsMouseButtonPressed(button);
            public static bool IsMouseButtonDown(int button) => RaylibPInvoke.IsMouseButtonDown(button);
            public static bool IsMouseButtonReleased(int button) => RaylibPInvoke.IsMouseButtonReleased(button);
            public static int GetMouseX() => RaylibPInvoke.GetMouseX();
            public static int GetMouseY() => RaylibPInvoke.GetMouseY();
            public static void SetConfigFlags(ConfigFlags flags) => RaylibPInvoke.SetConfigFlags(flags);
            public static void ToggleFullscreen() => RaylibPInvoke.ToggleFullscreen();
            public static bool IsWindowFullscreen() => RaylibPInvoke.IsWindowFullscreen();
            public static int GetMonitorWidth(int monitor) => RaylibPInvoke.GetMonitorWidth(monitor);
            public static int GetMonitorHeight(int monitor) => RaylibPInvoke.GetMonitorHeight(monitor);
            public static Texture2D LoadTexture(string fileName) => RaylibPInvoke.LoadTexture(fileName);
            public static void UnloadTexture(Texture2D texture) => RaylibPInvoke.UnloadTexture(texture);
            public static void DrawTexture(Texture2D texture, int posX, int posY, Color tint) => RaylibPInvoke.DrawTexture(texture, posX, posY, tint);
            public static void DrawTextureRec(Texture2D texture, Rectangle source, Vector2 position, Color tint) => RaylibPInvoke.DrawTextureRec(texture, source, position, tint);
            public static void DrawTexturePro(Texture2D texture, Rectangle source, Rectangle dest, Vector2 origin, float rotation, Color tint) => RaylibPInvoke.DrawTexturePro(texture, source, dest, origin, rotation, tint);
            public static RenderTexture2D LoadRenderTexture(int width, int height) => RaylibPInvoke.LoadRenderTexture(width, height);
            public static void UnloadRenderTexture(RenderTexture2D target) => RaylibPInvoke.UnloadRenderTexture(target);
            public static void BeginTextureMode(RenderTexture2D target) => RaylibPInvoke.BeginTextureMode(target);
            public static void EndTextureMode() => RaylibPInvoke.EndTextureMode();
        }
    }
}
